services:
  kimbal-logic-backend:
    build:
      context: ./src
      dockerfile: Dockerfile
    container_name: kimbal-logic-backend
    hostname: kimbal-logic-backend
    env_file: "./src/.env"
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
    depends_on:
      kimbal-logic-db:
        condition: service_healthy
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:8000/health || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  kimbal-logic-db:
    image: postgres:17
    container_name: kimbal-logic-db
    env_file: "./src/.env"
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: [ "CMD", "pg_isready", "-U", "${POSTGRES_USER}", "-d", "${POSTGRES_DB}" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
volumes:
  postgres_data:
