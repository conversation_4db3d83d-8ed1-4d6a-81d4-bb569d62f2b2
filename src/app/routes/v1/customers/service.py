from typing import List, Optional

from database.core import DbSession
from db_services import customer as customer_db_services
from entities.customers import Customer
from entities.states import State
from fastapi import HTTPException, status
from routes.v1.auth.service import CurrentUser
from utils.logger import get_service_logger

from . import models

logger = get_service_logger("customers_service")


def get_all_customers(
    db: DbSession,
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
) -> List[Customer]:
    """
    Get all customers from database
    """
    try:
        total = customer_db_services.get_customers_count_query(db, search=search)

        customers = customer_db_services.get_all_customers_query(
            db, skip=skip, limit=limit, search=search
        )

        has_next = (skip + limit) < total

        result = models.PaginatedCustomerResponse(
            customers=customers, total=total, skip=skip, limit=limit, has_next=has_next
        )

        return result

    except Exception as e:
        logger.error(f"Error retrieving customers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving customers",
        )


def validate_and_get_states(db: DbSession, state_ids: List[int]) -> List[State]:
    """
    Validate that all state_ids exist and return State objects
    """
    if not state_ids:  # Handle empty list
        return []

    states = db.query(State).filter(State.id.in_(state_ids)).all()
    found_state_ids = [state.id for state in states]
    missing_state_ids = set(state_ids) - set(found_state_ids)

    if missing_state_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"States not found: {', '.join(sorted(map(str, missing_state_ids)))}",
        )

    return states


def create_customer(
    db: DbSession,
    current_user: CurrentUser,
    customer_create: models.CustomerCreate,
) -> Customer:
    """
    Create a new customer with state associations
    """
    try:
        # Validate states exist if state_ids are provided
        validated_states = []
        if customer_create.state_ids:
            validated_states = validate_and_get_states(db, customer_create.state_ids)

        # Create customer object
        new_customer = Customer(
            customer_name=customer_create.customer_name,
            onboarded_date=customer_create.onboarded_date,
            created_by=current_user.user_id,
            updated_by=current_user.user_id,
            states=validated_states,
        )

        return customer_db_services.create_customer_query(db, new_customer)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating customer: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating customer",
        )


def get_customer_by_id(db: DbSession, id: int) -> Optional[Customer]:
    """
    Get a customer by its ID
    """

    customer = customer_db_services.get_customer_by_id_query(db, id)
    return customer


def update_customer(
    db: DbSession,
    current_user: CurrentUser,
    customer_id: int,
    customer_update: models.CustomerUpdateRequest,
) -> Customer:
    """
    Update customer by ID with partial data including states relationship
    """
    try:
        # Get existing customer
        existing_customer = customer_db_services.get_customer_by_id_query(
            db, customer_id
        )
        if not existing_customer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Customer with id {customer_id} not found",
            )

        # Get update data and extract state_ids for separate handling
        update_data = customer_update.model_dump(exclude_unset=True)
        state_ids = update_data.pop("state_ids", None)  # Extract state_ids

        # Handle states relationship update if state_ids provided
        if state_ids is not None:
            validated_states = validate_and_get_states(db, state_ids)
            existing_customer.states = validated_states  # Full replacement

        existing_customer.updated_by = current_user.user_id

        return customer_db_services.update_customer_query(
            db, existing_customer, update_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating customer: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating customer",
        )


def delete_customer(db: DbSession, customer: Customer) -> None:
    """
    Delete customer by ID
    """
    try:
        logger.info(f"Deleting customer with ID {customer.id}")
        customer_db_services.delete_customer_query(db, customer)
    except Exception as e:
        logger.error(f"Error deleting customer: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting customer",
        )
