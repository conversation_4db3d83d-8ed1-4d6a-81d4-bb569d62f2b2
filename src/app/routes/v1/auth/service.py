import os
from datetime import datetime, timedelta, timezone
from typing import Annotated
from uuid import UUID, uuid4

import jwt
from database.core import DbSession
from db_services import users as users_db_service
from entities.users import User
from fastapi import Depends, HTTPException, Request, status
from fastapi.security import OA<PERSON>2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm
from jwt import PyJWTError
from passlib.context import CryptContext
from sqlalchemy.exc import IntegrityError
from utils.exceptions import AuthenticationError
from utils.logger import get_service_logger

from .models import (
    CreateUserRequest,
    ForgotPasswordRequest,
    RefreshTokenRequest,
    ResetPassword,
    Token,
    TokenData,
    UserResponse,
)

logger = get_service_logger("auth")

SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", 30))
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", 7))

oauth2_bearer = OAuth2PasswordBearer(tokenUrl="v1/auth/login")
bcrypt_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return bcrypt_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    return bcrypt_context.hash(password)


def authenticate_user(email: str, password: str, db: DbSession) -> User | bool:
    user = users_db_service.get_user_by_email(db, email)
    if not user or not verify_password(password, user.password):
        logger.warning(f"Authentication failed: {email}")
        return False
    return user


def create_user(create_user_request: CreateUserRequest, db: DbSession) -> UserResponse:
    logger.info(f"Creating user: {create_user_request.email}")
    try:
        create_user_model = User(
            id=uuid4(),
            email=create_user_request.email,
            first_name=create_user_request.first_name,
            last_name=create_user_request.last_name,
            password=get_password_hash(create_user_request.password),
        )
        user = users_db_service.create_user(db, create_user_model)
        return UserResponse.model_validate(user)
    except IntegrityError:
        db.rollback()
        logger.warning(f"User already exists: {create_user_request.email}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="User with this email already exists",
        )
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to create user {create_user_request.email}: {str(e)}")
        raise


def create_access_token(email: str, user_id: UUID, expires_delta: timedelta) -> str:
    encode = {
        "sub": email,
        "id": str(user_id),
        "type": "access",
        "exp": datetime.now(timezone.utc) + expires_delta,
    }
    return jwt.encode(encode, SECRET_KEY, algorithm=ALGORITHM)


def create_refresh_token(email: str, user_id: UUID, expires_delta: timedelta) -> str:
    encode = {
        "sub": email,
        "id": str(user_id),
        "type": "refresh",
        "exp": datetime.now(timezone.utc) + expires_delta,
    }
    return jwt.encode(encode, SECRET_KEY, algorithm=ALGORITHM)


def login_for_access_token(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()], db: DbSession
) -> Token:
    user = authenticate_user(form_data.username, form_data.password, db)
    if not user:
        raise AuthenticationError()

    access_token = create_access_token(
        user.email, user.id, timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    refresh_token = create_refresh_token(
        user.email, user.id, timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    )

    return Token(
        access_token=access_token, refresh_token=refresh_token, token_type="bearer"
    )


def verify_token(token: str, token_type: str = "access"):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("id")
        token_type_from_payload: str = payload.get("type")

        # Verify token type matches expected type
        if token_type_from_payload != token_type:
            logger.warning(
                f"Token type mismatch: expected {token_type}, got {token_type_from_payload}"
            )
            raise AuthenticationError("Invalid token type")

        return TokenData(user_id=user_id)
    except PyJWTError as e:
        logger.warning(f"Token verification failed: {str(e)}")
        raise AuthenticationError()


def verify_refresh_token(refresh_token: str, token_type: str = "refresh") -> bool:
    try:
        payload = jwt.decode(refresh_token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("id")
        token_type_from_payload: str = payload.get("type")

        # Verify token type matches expected type
        if token_type_from_payload != token_type:
            logger.warning(
                f"Token type mismatch: expected {token_type}, got {token_type_from_payload}"
            )
            raise AuthenticationError("Invalid token type")

        return True
    except PyJWTError as e:
        logger.warning(f"Token verification failed: {str(e)}")
        raise AuthenticationError()


def refresh_access_token(refresh_request: RefreshTokenRequest, db: DbSession) -> Token:
    try:
        # Verify refresh token and get token data
        payload = jwt.decode(
            refresh_request.refresh_token, SECRET_KEY, algorithms=[ALGORITHM]
        )
        user_id: int = int(payload.get("id"))
        token_type_from_payload: str = payload.get("type")

        # Verify token type matches expected type
        if token_type_from_payload != "refresh":
            logger.warning(
                f"Token type mismatch: expected refresh, got {token_type_from_payload}"
            )
            raise AuthenticationError("Invalid token type")

        # Get user from database to ensure they still exist
        user = users_db_service.get_user_by_id(db, user_id)
        if not user:
            logger.warning(f"User not found for token refresh: {user_id}")
            raise AuthenticationError("User not found")

        # Create new tokens
        new_access_token = create_access_token(
            user.email, user.id, timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        new_refresh_token = create_refresh_token(
            user.email, user.id, timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        )

        logger.info(f"Token refreshed for user: {user.email}")
        return Token(
            access_token=new_access_token,
            refresh_token=new_refresh_token,
            token_type="bearer",
        )
    except PyJWTError as e:
        logger.warning(f"JWT validation failed: {str(e)}")
        raise AuthenticationError("Invalid refresh token")
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise AuthenticationError("Invalid refresh token")


def get_current_user(token: Annotated[str, Depends(oauth2_bearer)]) -> TokenData:
    return verify_token(token)


CurrentUser = Annotated[TokenData, Depends(get_current_user)]


def generate_reset_token(email: str) -> str:
    """Generate a password reset token that expires in 1 hour"""
    payload = {
        "email": email,
        "exp": datetime.now(timezone.utc) + timedelta(hours=1),
        "type": "password_reset",
    }
    return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)


def verify_reset_token(token: str) -> str:
    """Verify password reset token and return email"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("email")
        token_type = payload.get("type")

        if not email or token_type != "password_reset":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid reset token"
            )
        return email
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Reset token has expired"
        )
    except PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid reset token"
        )


def forgot_password(
    fpr: ForgotPasswordRequest,
    request: Request,
    db: DbSession,
):
    reset_token = None

    user = users_db_service.get_user_by_email(db, fpr.email)

    reset_token = generate_reset_token(fpr.email)
    if user:
        logger.info(f"Password reset token generated for user: {fpr.email}")
        # TODO: Send the reset token via email
    else:
        logger.debug(f"Password reset requested for non-existent email: {fpr.email}")

    return {
        "message": "Forgot password request processed",
        "token": f"{reset_token}",  # TODO: Token not to be returned. Will be sent via email
    }


def reset_password(rfp: ResetPassword, db: DbSession):
    email = verify_reset_token(rfp.reset_token)
    user = users_db_service.get_user_by_email(db, email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid reset token"
        )
    try:
        hashed_password = get_password_hash(rfp.new_password)
        user.password = hashed_password
        user = users_db_service.update_user(db, user)
        return {"message": "Password reset successful"}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset password",
        )
