from typing import Dict, List, Optional

from entities.customers import Customer
from sqlalchemy import or_
from sqlalchemy.orm import Session


def get_all_customers_query(
    db: Session, skip: int = 0, limit: int = 100, search: Optional[str] = None
) -> List[Customer]:
    """
    Query to fetch all deleted customers from the database.
    """
    query = db.query(Customer)

    if search and search.strip():
        search_term = f"%{search.strip()}%"
        search_filter = or_(
            Customer.customer_name.ilike(search_term),
            Customer.customer_id.ilike(search_term),
        )
        query = query.filter(search_filter)

    return query.filter(Customer.is_deleted == False).offset(skip).limit(limit).all()


def get_customers_count_query(db: Session, search: Optional[str] = None) -> int:
    """
    Query to get total count of customers
    """
    query = db.query(Customer)
    if search and search.strip():
        search_term = f"%{search.strip()}%"
        search_filter = or_(
            Customer.customer_name.ilike(search_term),
            Customer.customer_id.ilike(search_term),
        )
        query = query.filter(search_filter)

    return query.filter(Customer.is_deleted == False).count()


def get_customer_by_id_query(db: Session, customer_id: int) -> Customer:
    """
    Query to fetch a customer by ID.
    """

    return (
        db.query(Customer)
        .filter(Customer.id == customer_id, Customer.is_deleted == False)
        .first()
    )


def create_customer_query(db: Session, customer: Customer) -> Customer:
    """
    Create customer in database with auto-generated customer_id.
    """
    db.add(customer)
    db.commit()
    db.refresh(customer)

    return customer


def update_customer_query(
    db: Session, customer: Customer, update_data: Optional[Dict] = None
) -> Customer:
    """
    Query to update an existing customer.
    """
    if update_data:
        for field, value in update_data.items():
            if hasattr(customer, field):
                setattr(customer, field, value)

    db.commit()
    db.refresh(customer)
    return customer


def delete_customer_query(db: Session, customer: Customer) -> None:
    """
    Query to delete a customer (soft delete).
    """
    customer.is_deleted = True
    db.commit()
