import enum

from database.core import Base
from sqlalchemy import Column, Enum, Integer, String


class MarketTypeEnum(enum.Enum):
    DAM = "DAM"
    GDAM = "GDAM"
    RTM = "RTM"
    GRTM = "GRTM"
    # Add more as needed


class RenewableEnergyTypeEnum(enum.Enum):
    SOLAR = "solar"
    WIND = "wind"
    HYDRO = "hydro"
    # Add more as needed


class SettlementTypeEnum(enum.Enum):
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    DAILY = "daily"
    # Add more as needed (don't know whether its correct types)


class ConditionTypeEnum(enum.Enum):
    LESS_THAN = "less_than"
    GREATER_THAN = "greater_than"
    EQUAL = "EQUAL"
    # Add more as needed


class MarketType(Base):
    __tablename__ = "market_types"
    id = Column(Integer, primary_key=True)
    market_type = Column(Enum(MarketTypeEnum), unique=True)


class RenewableEnergyType(Base):
    __tablename__ = "renewable_energy_types"
    id = Column(Integer, primary_key=True)
    renewable_energy_type = Column(Enum(RenewableEnergyTypeEnum), unique=True)


class SettlementType(Base):
    __tablename__ = "settlement_types"
    id = Column(Integer, primary_key=True)
    settlement_type = Column(Enum(SettlementTypeEnum), unique=True)


class ConditionType(Base):
    __tablename__ = "condition_types"
    id = Column(Integer, primary_key=True)
    condition = Column(Enum(ConditionTypeEnum), unique=True)


class Ratio_value(Base):
    __tablename__ = "ratio_values"

    id = Column(Integer, primary_key=True)
    ratio_value = Column(Integer, unique=True, nullable=False)


class Factor(Base):
    __tablename__ = "factors"

    id = Column(Integer, primary_key=True)
    factor = Column(String(100), unique=True, nullable=False)


class Carry_forwards(Base):
    __tablename__ = "carry_forwards"

    id = Column(Integer, primary_key=True)
    carry_forward = Column(String(100), unique=True, nullable=False)
