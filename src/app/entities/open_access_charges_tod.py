from database.core import Base
from sqlalchemy import (
    Column,
    Date,
    DateTime,
    ForeignKey,
    Integer,
    Numeric,
    String,
    Time,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class OpenAccessChargesTod(Base):
    __tablename__ = "open_access_charges_tod"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column((UUID(as_uuid=True)), ForeignKey("logics.id"), nullable=False)
    name = Column(String(255))
    start_time_block = Column(Time)
    start_time_block_start_date = Column(Date)
    end_time_block = Column(Time)
    end_time_block_start_date = Column(Date)
    base_discom_tariff = Column(Numeric(15, 4))
    base_discom_tariff_start_date = Column(Date)
    rebate = Column(Numeric(15, 4))
    rebate_start_date = Column(Date)
    fuel_surcharge = Column(Numeric(15, 4))
    fuel_surcharge_start_date = Column(Date)
    total_discom_landed_tariff = Column(Numeric(15, 4))
    total_discom_landed_tariff_start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="open_access_charges_tod")
