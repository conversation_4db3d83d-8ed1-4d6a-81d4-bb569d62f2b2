import uuid

from database.core import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    first_name = Column(String(100))
    last_name = Column(String(100))
    email = Column(String(255), unique=True, index=True)
    password = Column(Text)
    role = Column(String(50), server_default="admin")
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    created_customers = relationship(
        "Customer", foreign_keys="Customer.created_by", back_populates="creator"
    )
    updated_customers = relationship(
        "Customer", foreign_keys="Customer.updated_by", back_populates="updater"
    )

    created_logics = relationship(
        "Logic", foreign_keys="Logic.created_by", back_populates="creator"
    )
    updated_logics = relationship(
        "Logic", foreign_keys="Logic.updated_by", back_populates="updater"
    )

    def __repr__(self):
        return f"<User(email='{self.email}', first_name='{self.first_name}', last_name='{self.last_name}')>"
