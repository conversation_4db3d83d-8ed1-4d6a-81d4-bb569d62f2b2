import uuid

from database.core import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Date<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .customer_logics import customer_logics


class Logic(Base):
    __tablename__ = "logics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    logic_id = Column(String(100), unique=True)
    logic_name = Column(String(255))
    is_deleted = Column(Boolean, default=False)
    is_draft = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))

    # Relationships

    creator = relationship(
        "User", foreign_keys=[created_by], back_populates="created_logics"
    )
    updater = relationship(
        "User", foreign_keys=[updated_by], back_populates="updated_logics"
    )

    demands = relationship("Demand", back_populates="logic")
    market_related_data = relationship("MarketRelatedData", back_populates="logic")
    solar_and_other_re = relationship("SolarAndOtherRe", back_populates="logic")
    open_access_charge_general_information = relationship(
        "OpenAccessChargeGeneralInformation", back_populates="logic"
    )
    open_access_charges_tod = relationship(
        "OpenAccessChargesTod", back_populates="logic"
    )
    list_of_losses = relationship("ListOfLosses", back_populates="logic")
    customer_and_state = relationship("CustomerAndState", back_populates="logic")
    settlements = relationship("Settlement", back_populates="logic")
    conditions = relationship("Condition", back_populates="logic")
    deployments = relationship("Deployment", back_populates="logic")
    forward_testings = relationship("ForwardTesting", back_populates="logic")
    backward_testings = relationship("BackwardTesting", back_populates="logic")

    # Many-to-many with Customer
    customers = relationship(
        "Customer", secondary=customer_logics, back_populates="logics"
    )
