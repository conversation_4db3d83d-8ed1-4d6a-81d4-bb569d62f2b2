from database.core import Base
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    DateTime,
    ForeignKey,
    Integer,
    Numeric,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class SolarAndOtherRe(Base):
    __tablename__ = "solar_and_other_re"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    re_type = Column(String(100))
    capacity = Column(Numeric(15, 4))
    cuf = Column(Numeric(15, 4))
    levelized_tariff_power_source = Column(Numeric(15, 4))
    percentage_of_demand_met_by_re_source = Column(Numeric(15, 4))
    banking_allowed = Column(Boolean, default=False)
    banking_allowed_description = Column(Text)
    banking_charges = Column(Numeric(15, 4))
    percentage_banking_allowed = Column(Numeric(15, 4))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="solar_and_other_re")
