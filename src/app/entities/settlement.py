from database.core import Base
from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship


class Settlement(Base):
    __tablename__ = "settlement"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    settlement_type = Column(String(100))

    # Relationships
    logic = relationship("Logic", back_populates="settlements")
