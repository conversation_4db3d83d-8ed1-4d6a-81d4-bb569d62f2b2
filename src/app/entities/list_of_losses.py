from database.core import Base
from sqlalchemy import Column, Date, DateTime, <PERSON><PERSON>ey, Integer, Numeric
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class ListOfLosses(Base):
    __tablename__ = "list_of_losses"

    id = Column(Integer, primary_key=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    inter_state_transmission_losses = Column(Numeric(15, 4))
    inter_state_transmission_losses_start_date = Column(Date)
    intra_state_transmission_losses = Column(Numeric(15, 4))
    intra_state_transmission_losses_start_date = Column(Date)
    wheeling_losses = Column(Numeric(15, 4))
    wheeling_losses_start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="list_of_losses")
