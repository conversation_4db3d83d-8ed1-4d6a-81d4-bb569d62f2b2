from database.core import Base
from sqlalchemy import <PERSON>umn, Foreign<PERSON>ey, Integer, Numeric, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship


class Condition(Base):
    __tablename__ = "conditions"

    id = Column(Integer, primary_key=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    ratio_value = Column(Numeric(15, 4))
    condition = Column(String(255))
    factor = Column(String(255))
    goal_parameter = Column(String(255))
    operating_value = Column(Numeric(15, 4))

    # Relationships
    logic = relationship("Logic", back_populates="conditions")
