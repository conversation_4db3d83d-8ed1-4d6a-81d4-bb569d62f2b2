from database.core import Base
from sqlalchemy import Column, Date, DateTime, Foreign<PERSON>ey, Integer, Numeric, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class OpenAccessChargeGeneralInformation(Base):
    __tablename__ = "open_access_charge_general_information"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column((UUID(as_uuid=True)), ForeignKey("logics.id"))
    name = Column(String(255))
    interstate_transmission_charges = Column(Numeric(15, 4))
    interstate_transmission_charges_start_date = Column(Date)
    intrastate_transmission_charges = Column(Numeric(15, 4))
    intrastate_transmission_charges_start_date = Column(Date)
    wheeling_charges = Column(Numeric(15, 4))
    wheeling_charges_start_date = Column(Date)
    cross_subsidy_surcharges = Column(Numeric(15, 4))
    cross_subsidy_surcharges_start_date = Column(Date)
    additional_surcharges = Column(Numeric(15, 4))
    additional_surcharges_start_date = Column(Date)
    scheduling_and_other_charges = Column(Numeric(15, 4))
    scheduling_and_other_charges_start_date = Column(Date)
    iex_transaction_fees = Column(Numeric(15, 4))
    iex_transaction_fees_start_date = Column(Date)
    rec_charges = Column(Numeric(15, 4))
    rec_charges_start_date = Column(Date)
    dsm_charges = Column(Numeric(15, 4))
    dsm_charges_start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship(
        "Logic", back_populates="open_access_charge_general_information"
    )
