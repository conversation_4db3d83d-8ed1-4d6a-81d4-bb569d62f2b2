"""
Tests for customer service layer - mocks database layer and tests business logic
"""
from datetime import date
from unittest.mock import Mock, patch
from uuid import uuid4

import pytest
from fastapi import HTTPException, status

from app.routes.v1.customers.models import CustomerCreate, CustomerUpdateRequest
from app.routes.v1.customers.service import (
    create_customer,
    delete_customer,
    get_all_customers,
    get_customer_by_id,
    update_customer,
    validate_and_get_states,
)


class TestValidateAndGetStates:
    """Test state validation functionality"""

    @patch("app.routes.v1.customers.service.state_db_services.get_states_by_ids")
    def test_validate_and_get_states_success(self, mock_get_states):
        """Test successful state validation"""
        # Mock states
        mock_state1 = Mock()
        mock_state1.id = 1
        mock_state1.name = "Kerala"
        
        mock_state2 = Mock()
        mock_state2.id = 2
        mock_state2.name = "Karnataka"
        
        mock_get_states.return_value = [mock_state1, mock_state2]
        
        mock_db = Mock()
        result = validate_and_get_states(mock_db, [1, 2])
        
        assert len(result) == 2
        assert result[0].id == 1
        assert result[1].id == 2
        mock_get_states.assert_called_once_with(mock_db, [1, 2])

    @patch("app.routes.v1.customers.service.state_db_services.get_states_by_ids")
    def test_validate_and_get_states_not_found(self, mock_get_states):
        """Test state validation with non-existent states"""
        # Mock only one state found when two requested
        mock_state1 = Mock()
        mock_state1.id = 1
        mock_state1.name = "Kerala"
        
        mock_get_states.return_value = [mock_state1]  # Only one state found
        
        mock_db = Mock()
        
        with pytest.raises(HTTPException) as exc_info:
            validate_and_get_states(mock_db, [1, 999])  # 999 doesn't exist
        
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid state IDs" in str(exc_info.value.detail)


class TestCreateCustomer:
    """Test customer creation service"""

    @patch("app.routes.v1.customers.service.validate_and_get_states")
    @patch("app.routes.v1.customers.service.customer_db_services.create_customer_query")
    def test_create_customer_success(self, mock_create_query, mock_validate_states):
        """Test successful customer creation"""
        # Mock states
        mock_state1 = Mock()
        mock_state1.id = 1
        mock_state1.name = "Kerala"
        mock_validate_states.return_value = [mock_state1]
        
        # Mock created customer
        mock_customer = Mock()
        mock_customer.id = 1
        mock_customer.customer_id = "EUWI-0001"
        mock_customer.customer_name = "Test Customer"
        mock_customer.onboarded_date = date(2024, 1, 15)
        mock_customer.states = [mock_state1]
        mock_create_query.return_value = mock_customer
        
        # Mock current user
        mock_user = Mock()
        mock_user.user_id = uuid4()
        
        # Create request
        request = CustomerCreate(
            customer_name="Test Customer",
            onboarded_date=date(2024, 1, 15),
            state_ids=[1]
        )
        
        mock_db = Mock()
        result = create_customer(mock_db, mock_user, request)
        
        assert result == mock_customer
        mock_validate_states.assert_called_once_with(mock_db, [1])
        mock_create_query.assert_called_once()

    @patch("app.routes.v1.customers.service.validate_and_get_states")
    @patch("app.routes.v1.customers.service.customer_db_services.create_customer_query")
    def test_create_customer_without_states(self, mock_create_query, mock_validate_states):
        """Test customer creation without states"""
        # Mock created customer
        mock_customer = Mock()
        mock_customer.id = 1
        mock_customer.customer_id = "EUWI-0001"
        mock_customer.customer_name = "Test Customer"
        mock_customer.onboarded_date = date(2024, 1, 15)
        mock_customer.states = []
        mock_create_query.return_value = mock_customer
        
        # Mock current user
        mock_user = Mock()
        mock_user.user_id = uuid4()
        
        # Create request without states
        request = CustomerCreate(
            customer_name="Test Customer",
            onboarded_date=date(2024, 1, 15),
            state_ids=[]
        )
        
        mock_db = Mock()
        result = create_customer(mock_db, mock_user, request)
        
        assert result == mock_customer
        mock_validate_states.assert_not_called()  # Should not validate empty states
        mock_create_query.assert_called_once()

    @patch("app.routes.v1.customers.service.validate_and_get_states")
    @patch("app.routes.v1.customers.service.customer_db_services.create_customer_query")
    def test_create_customer_database_error(self, mock_create_query, mock_validate_states):
        """Test customer creation with database error"""
        mock_validate_states.return_value = []
        mock_create_query.side_effect = Exception("Database error")
        
        mock_user = Mock()
        mock_user.user_id = uuid4()
        
        request = CustomerCreate(
            customer_name="Test Customer",
            onboarded_date=date(2024, 1, 15),
            state_ids=[]
        )
        
        mock_db = Mock()
        
        with pytest.raises(HTTPException) as exc_info:
            create_customer(mock_db, mock_user, request)
        
        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Error creating customer" in str(exc_info.value.detail)
        mock_db.rollback.assert_called_once()


class TestGetCustomerById:
    """Test customer retrieval by ID"""

    @patch("app.routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    def test_get_customer_by_id_success(self, mock_get_query):
        """Test successful customer retrieval"""
        mock_customer = Mock()
        mock_customer.id = 1
        mock_customer.customer_name = "Test Customer"
        mock_get_query.return_value = mock_customer
        
        mock_db = Mock()
        result = get_customer_by_id(mock_db, 1)
        
        assert result == mock_customer
        mock_get_query.assert_called_once_with(mock_db, 1)

    @patch("app.routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    def test_get_customer_by_id_not_found(self, mock_get_query):
        """Test customer retrieval when not found"""
        mock_get_query.return_value = None
        
        mock_db = Mock()
        result = get_customer_by_id(mock_db, 999)
        
        assert result is None
        mock_get_query.assert_called_once_with(mock_db, 999)


class TestUpdateCustomer:
    """Test customer update service"""

    @patch("app.routes.v1.customers.service.validate_and_get_states")
    @patch("app.routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    @patch("app.routes.v1.customers.service.customer_db_services.update_customer_query")
    def test_update_customer_success(self, mock_update_query, mock_get_query, mock_validate_states):
        """Test successful customer update"""
        # Mock existing customer
        mock_customer = Mock()
        mock_customer.id = 1
        mock_customer.customer_name = "Old Name"
        mock_customer.states = []
        mock_get_query.return_value = mock_customer
        
        # Mock updated customer
        mock_updated_customer = Mock()
        mock_updated_customer.id = 1
        mock_updated_customer.customer_name = "New Name"
        mock_update_query.return_value = mock_updated_customer
        
        # Mock states
        mock_state = Mock()
        mock_state.id = 1
        mock_validate_states.return_value = [mock_state]
        
        # Mock current user
        mock_user = Mock()
        mock_user.user_id = uuid4()
        
        # Update request
        request = CustomerUpdateRequest(
            customer_name="New Name",
            state_ids=[1]
        )
        
        mock_db = Mock()
        result = update_customer(mock_db, mock_user, 1, request)
        
        assert result == mock_updated_customer
        mock_get_query.assert_called_once_with(mock_db, 1)
        mock_validate_states.assert_called_once_with(mock_db, [1])
        mock_update_query.assert_called_once()

    @patch("app.routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    def test_update_customer_not_found(self, mock_get_query):
        """Test updating non-existent customer"""
        mock_get_query.return_value = None
        
        mock_user = Mock()
        mock_user.user_id = uuid4()
        
        request = CustomerUpdateRequest(customer_name="New Name")
        
        mock_db = Mock()
        
        with pytest.raises(HTTPException) as exc_info:
            update_customer(mock_db, mock_user, 999, request)
        
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Customer with id 999 not found" in str(exc_info.value.detail)


class TestDeleteCustomer:
    """Test customer deletion service"""

    @patch("app.routes.v1.customers.service.customer_db_services.delete_customer_query")
    def test_delete_customer_success(self, mock_delete_query):
        """Test successful customer deletion"""
        mock_customer = Mock()
        mock_customer.id = 1
        mock_customer.is_deleted = False
        
        mock_db = Mock()
        delete_customer(mock_db, mock_customer)
        
        mock_delete_query.assert_called_once_with(mock_db, mock_customer)


class TestGetAllCustomers:
    """Test customer listing service"""

    @patch("app.routes.v1.customers.service.customer_db_services.get_customers_count_query")
    @patch("app.routes.v1.customers.service.customer_db_services.get_all_customers_query")
    def test_get_all_customers_success(self, mock_get_all_query, mock_count_query):
        """Test successful customer listing"""
        # Mock customers
        mock_customers = [Mock(), Mock()]
        mock_get_all_query.return_value = mock_customers
        mock_count_query.return_value = 2
        
        mock_db = Mock()
        result = get_all_customers(mock_db, skip=0, limit=100)
        
        assert result["customers"] == mock_customers
        assert result["total"] == 2
        assert result["skip"] == 0
        assert result["limit"] == 100
        assert result["has_next"] is False
        
        mock_get_all_query.assert_called_once_with(mock_db, skip=0, limit=100, search=None)
        mock_count_query.assert_called_once_with(mock_db, search=None)

    @patch("app.routes.v1.customers.service.customer_db_services.get_customers_count_query")
    @patch("app.routes.v1.customers.service.customer_db_services.get_all_customers_query")
    def test_get_all_customers_with_pagination(self, mock_get_all_query, mock_count_query):
        """Test customer listing with pagination"""
        mock_customers = [Mock()]
        mock_get_all_query.return_value = mock_customers
        mock_count_query.return_value = 10  # Total count
        
        mock_db = Mock()
        result = get_all_customers(mock_db, skip=5, limit=5)
        
        assert result["customers"] == mock_customers
        assert result["total"] == 10
        assert result["skip"] == 5
        assert result["limit"] == 5
        assert result["has_next"] is True  # 5 + 5 < 10, so there are more
        
        mock_get_all_query.assert_called_once_with(mock_db, skip=5, limit=5, search=None)
        mock_count_query.assert_called_once_with(mock_db, search=None)
