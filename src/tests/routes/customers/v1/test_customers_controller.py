# tests/routes/customers/v1/test_customers_controller.py
from datetime import date
from unittest.mock import Mock, patch

from fastapi import status
from fastapi.testclient import TestClient


def test_create_customer_success(client: TestClient, auth_headers):
    """Test successful customer creation"""

    customer_data = {
        "customer_name": "Test Customer",
        "onboarded_date": "2024-01-15",
        "is_deleted": False,
        "state_ids": [1, 2],  # Use integer IDs instead of UUIDs
    }

    # Mock states with proper structure
    mock_state1 = Mock()
    mock_state1.id = 1
    mock_state1.name = "Kerala"

    mock_state2 = Mock()
    mock_state2.id = 2
    mock_state2.name = "Karnataka"

    # Mock the database service to return a proper customer object
    mock_customer = Mock()
    mock_customer.id = 1
    mock_customer.customer_id = "EUWI-0001"
    mock_customer.customer_name = "Test Customer"
    mock_customer.onboarded_date = date(2024, 1, 15)
    mock_customer.is_deleted = False
    mock_customer.states = [mock_state1, mock_state2]

    # Mock the service layer - use the correct import path from controller
    with patch("app.routes.v1.customers.controller.service.create_customer", return_value=mock_customer):
        response = client.post(
            "/v1/customers/", json=customer_data, headers=auth_headers
        )

        assert response.status_code == status.HTTP_201_CREATED


def test_create_customer_validation_error(client: TestClient, auth_headers):
    """Test customer creation with invalid data"""

    invalid_data = {
        "customer_name": "",  # Empty name
        "onboarded_date": "invalid-date",  # Invalid date format
    }

    response = client.post("/v1/customers/", json=invalid_data, headers=auth_headers)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


def test_get_customer_by_id_success(client: TestClient, auth_headers):
    """Test successful customer retrieval by ID"""

    customer_id = 1

    # Mock the database service to return a proper customer object
    mock_customer = Mock()
    mock_customer.id = customer_id
    mock_customer.customer_id = "EUWI-0001"
    mock_customer.customer_name = "Test Customer"
    mock_customer.onboarded_date = date(2024, 1, 15)
    mock_customer.is_deleted = False
    mock_customer.states = []

    with patch("app.routes.v1.customers.controller.service.get_customer_by_id", return_value=mock_customer):
        response = client.get(f"/v1/customers/{customer_id}", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK


def test_update_customer_not_found(client: TestClient, auth_headers):
    """Test updating non-existent customer"""

    customer_id = 999
    updated_data = {
        "customer_name": "Non-Existent Customer",
    }

    with patch("app.routes.v1.customers.controller.service.get_customer_by_id", return_value=None):
        response = client.patch(
            f"/v1/customers/{customer_id}",
            json=updated_data,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND


def test_update_customer_success(client: TestClient, auth_headers):
    """Test successful customer update"""

    customer_id = 1
    updated_data = {
        "customer_name": "Updated Customer",
    }

    # Mock the service to return an updated customer object
    mock_customer = Mock()
    mock_customer.id = customer_id
    mock_customer.customer_id = "EUWI-0001"
    mock_customer.customer_name = "Updated Customer"
    mock_customer.onboarded_date = date(2024, 1, 15)
    mock_customer.is_deleted = False
    mock_customer.states = []

    with patch("app.routes.v1.customers.controller.service.get_customer_by_id", return_value=mock_customer):
        with patch("app.routes.v1.customers.controller.service.update_customer", return_value=mock_customer):
            response = client.patch(
                f"/v1/customers/{customer_id}",
                json=updated_data,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK


def test_update_customer_partial_update(client: TestClient, auth_headers):
    """Test partial customer update"""

    customer_id = 1
    update_data = {
        "customer_name": "Partially Updated Name"
        # Only updating name, other fields remain unchanged
    }

    # Mock the service to return a partially updated customer object
    mock_customer = Mock()
    mock_customer.id = customer_id
    mock_customer.customer_id = "EUWI-0001"
    mock_customer.customer_name = "Partially Updated Name"
    mock_customer.onboarded_date = date(2024, 1, 15)
    mock_customer.is_deleted = False
    mock_customer.states = []

    with patch("app.routes.v1.customers.service.get_customer_by_id", return_value=mock_customer):
        with patch("app.routes.v1.customers.service.update_customer", return_value=mock_customer):
            response = client.patch(
                f"/v1/customers/{customer_id}", json=update_data, headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK


def test_list_customers_success(client: TestClient, auth_headers):
    """Test successful customer listing"""

    # Mock customers list
    mock_customers = []

    # Mock the paginated response
    mock_response = {
        "customers": mock_customers,
        "total": 0,
        "skip": 0,
        "limit": 100,
        "has_next": False
    }

    # Mock service layer
    with patch("app.routes.v1.customers.service.get_all_customers", return_value=mock_response):
        response = client.get("/v1/customers/", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert "customers" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
        assert "has_next" in data


def test_list_customers_with_pagination(client: TestClient, auth_headers):
    """Test customer listing with pagination"""

    # Mock customers list
    mock_customers = []

    # Mock the paginated response
    mock_response = {
        "customers": mock_customers,
        "total": 10,
        "skip": 5,
        "limit": 5,
        "has_next": False
    }

    # Mock service layer
    with patch("app.routes.v1.customers.service.get_all_customers", return_value=mock_response):
        response = client.get("/v1/customers/?skip=5&limit=5", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK


def test_delete_customer_success(client: TestClient, auth_headers):
    """Test successful customer soft deletion"""

    customer_id = 1

    # Mock an existing customer to be deleted
    mock_customer = Mock()
    mock_customer.id = customer_id
    mock_customer.is_deleted = False

    with patch("app.routes.v1.customers.service.get_customer_by_id", return_value=mock_customer):
        with patch("app.routes.v1.customers.service.delete_customer", return_value=None):
            response = client.delete(f"/v1/customers/{customer_id}", headers=auth_headers)

            assert response.status_code == status.HTTP_204_NO_CONTENT


def test_customer_endpoints_require_auth(client: TestClient):
    """Test that customer endpoints require authentication"""

    # Test GET without auth
    response = client.get("/v1/customers/")
    assert response.status_code == status.HTTP_401_UNAUTHORIZED

    # Test POST without auth
    response = client.post("/v1/customers/", json={})
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_create_customer_with_states(client: TestClient, auth_headers):
    """Test customer creation with state associations"""

    customer_data = {
        "customer_name": "Customer with States",
        "onboarded_date": "2024-01-15",
        "is_deleted": False,
        "state_ids": [1, 2, 3],  # Multiple state IDs
    }

    # Mock states with integer IDs
    mock_states = []
    for i in [1, 2, 3]:
        mock_state = Mock()
        mock_state.id = i
        mock_state.name = f"State {i}"
        mock_states.append(mock_state)

    mock_customer = Mock()
    mock_customer.id = 1
    mock_customer.customer_id = "EUWI-0001"
    mock_customer.customer_name = "Customer with States"
    mock_customer.onboarded_date = date(2024, 1, 15)
    mock_customer.is_deleted = False
    mock_customer.states = mock_states

    with patch("app.routes.v1.customers.service.create_customer", return_value=mock_customer):
        response = client.post(
            "/v1/customers/", json=customer_data, headers=auth_headers
        )

        assert response.status_code == status.HTTP_201_CREATED
