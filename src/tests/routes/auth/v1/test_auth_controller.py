from unittest.mock import Mock, patch

from fastapi.testclient import Test<PERSON><PERSON>

from app.routes.v1.auth.models import CreateUserRequest


def test_register_user(client: TestClient):
    """Test user registration with unique email"""
    register_data = CreateUserRequest(
        first_name="Test",
        last_name="User",
        email="<EMAIL>",
        password="testPassword@123",
    )

    # Mock that no user exists with this email
    with patch("db_services.users.get_user_by_email", return_value=None):
        response = client.post("/v1/auth/register", json=register_data.model_dump())
        assert response.status_code == 201


def test_register_duplicate_email(client: TestClient):
    """Test registration with an email that already exists"""
    from fastapi import HTTPException, status
    
    # Mock the service to succeed on first call, fail on second
    call_count = 0
    def mock_create_user(create_user_request, db):
        nonlocal call_count
        call_count += 1
        if call_count == 1:
            return {"message": "User Created Successfully"}
        else:
            # Simulate duplicate email error
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="User with this email already exists",
            )
    
    with patch("db_services.users.create_user", side_effect=mock_create_user):
        # First registration should succeed
        register_data = {
            "email": "<EMAIL>",
            "password": "testPassword@123",
            "first_name": "First",
            "last_name": "User",
        }
        response = client.post("/v1/auth/register", json=register_data)
        assert response.status_code == 201

        # Try to register with the same email - should fail
        response = client.post(
            "/v1/auth/register",
            json={
                "email": "<EMAIL>",  # Same email
                "password": "differentPassword@123",
                "first_name": "Another",
                "last_name": "User",
            },
        )
        # Should return conflict error
        assert response.status_code == 409


def test_register_with_invalid_password(client: TestClient):
    """Test registration with an invalid password (too short)"""
    response = client.post(
        "/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "short",  # Too short password
            "first_name": "Test",
            "last_name": "User",
        },
    )
    assert response.status_code == 422  # Validation error


def test_register_with_invalid_email(client: TestClient):
    """Test registration with an invalid email format"""
    response = client.post(
        "/v1/auth/register",
        json={
            "email": "not-an-email",  # Invalid email format
            "password": "testPassword@123",
            "first_name": "Test",
            "last_name": "User",
        },
    )
    assert response.status_code == 422  # Validation error
    assert "value is not a valid email address" in response.json()["detail"][0]["msg"]


def test_login(client: TestClient):
    """Test successful login"""
    # Generate a real bcrypt hash for the test password
    from passlib.context import CryptContext
    bcrypt_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    hashed_password = bcrypt_context.hash("testPassword@123")
    
    # Mock the database service to return a user with proper password hash
    mock_user = Mock()
    mock_user.id = "test-user-id"
    mock_user.email = "<EMAIL>"
    mock_user.password = hashed_password
    
    with patch("db_services.users.get_user_by_email", return_value=mock_user):
        login_response = client.post(
            "/v1/auth/login",
            data={
                "username": "<EMAIL>",
                "password": "testPassword@123",
                "grant_type": "password",
            },
        )
        assert login_response.status_code == 200
        token_data = login_response.json()
        assert "access_token" in token_data
        assert token_data["token_type"] == "bearer"
def test_login_nonexistent_user(client: TestClient):
    """Test login with non-existent user"""
    # Mock the database service to return None (user not found)
    with patch("db_services.users.get_user_by_email", return_value=None):
        response = client.post(
            "/v1/auth/login",
            data={
                "username": "<EMAIL>",
                "password": "wrongpassword",
                "grant_type": "password",
            },
        )
        assert response.status_code == 401


def test_login_wrong_password(client: TestClient):
    """Test login with wrong password"""
    # Generate a hash for a different password
    from passlib.context import CryptContext
    bcrypt_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    hashed_password = bcrypt_context.hash("correctPassword@123")  # Different from test input
    
    # Mock the database service to return a user with different password hash
    mock_user = Mock()
    mock_user.id = "test-user-id"
    mock_user.email = "<EMAIL>"
    mock_user.password = hashed_password
    
    with patch("db_services.users.get_user_by_email", return_value=mock_user):
        response = client.post(
            "/v1/auth/login",
            data={
                "username": "<EMAIL>",
                "password": "wrongpassword",  # Wrong password
                "grant_type": "password",
            },
        )
        assert response.status_code == 401
def test_forgot_password(client: TestClient):
    """Test forgot password endpoint"""
    # Mock a user exists
    mock_user = Mock()
    mock_user.id = 1
    mock_user.email = "<EMAIL>"
    
    with patch("db_services.users.get_user_by_email", return_value=mock_user):
        # Test forgot password
        response = client.post(
            "/v1/auth/forgot-password",
            json={"email": "<EMAIL>"},
        )
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Forgot password request processed"


def test_forgot_password_with_invalid_email(client: TestClient):
    """Test forgot password with invalid email"""
    response = client.post(
        "/v1/auth/forgot-password",
        json={"email": "not-an-email"},
    )
    assert response.status_code == 422
    assert "value is not a valid email address" in response.json()["detail"][0]["msg"]


def test_forgot_password_nonexistent_user(client: TestClient):
    """Test forgot password with non-existent user (should still return success for security)"""
    with patch("db_services.users.get_user_by_email", return_value=None):
        response = client.post(
            "/v1/auth/forgot-password",
            json={"email": "<EMAIL>"},
        )
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Forgot password request processed"


def test_reset_password_with_invalid_token(client: TestClient):
    """Test reset password with invalid token"""
    response = client.post(
        "/v1/auth/reset-password",
        json={
            "reset_token": "invalid-token",
            "new_password": "newPassword@123",
            "confirm_password": "newPassword@123",
        },
    )
    assert response.status_code == 400
    assert "Invalid reset token" in response.json()["detail"]


def test_reset_password_with_short_password(client: TestClient):
    """Test reset password with a password that's too short"""
    response = client.post(
        "/v1/auth/reset-password",
        json={
            "reset_token": "test-reset-token",
            "new_password": "short",
            "confirm_password": "short",
        },
    )
    assert response.status_code == 422


def test_reset_password_with_mismatched_passwords(client: TestClient):
    """Test reset password with mismatched passwords"""
    response = client.post(
        "/v1/auth/reset-password",
        json={
            "reset_token": "test-reset-token",
            "new_password": "newPassword@123",
            "confirm_password": "differentPassword@123",
        },
    )
    assert response.status_code == 422
