from datetime import timedelta
from unittest.mock import Mock, patch
from uuid import uuid4

import pytest
from fastapi import HTTP<PERSON><PERSON><PERSON>, status
from routes.v1.auth.models import CreateUserRequest
from routes.v1.auth.service import (
    authenticate_user,
    create_access_token,
    create_refresh_token,
    create_user,
    get_password_hash,
    verify_password,
)
from sqlalchemy.exc import IntegrityError


class TestPasswordFunctions:
    """Test password hashing and verification"""

    def test_password_hashing_and_verification(self):
        """Test that password hashing and verification work correctly"""
        password = "testPassword@123"
        hashed = get_password_hash(password)
        
        assert hashed != password  # Should be hashed
        assert verify_password(password, hashed)  # Should verify correctly
        assert not verify_password("wrongpassword", hashed)  # Should fail with wrong password


class TestTokenFunctions:
    """Test JWT token creation"""

    def test_create_access_token(self):
        """Test access token creation"""
        email = "<EMAIL>"
        user_id = uuid4()
        expires_delta = timedelta(minutes=30)
        
        token = create_access_token(email, user_id, expires_delta)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Decode and verify token content
        import jwt
        decoded = jwt.decode(token, "test_secret_key", algorithms=["HS256"])
        assert decoded["sub"] == email
        assert decoded["id"] == str(user_id)
        assert decoded["type"] == "access"

    def test_create_refresh_token(self):
        """Test refresh token creation"""
        email = "<EMAIL>"
        user_id = uuid4()
        expires_delta = timedelta(days=7)
        
        token = create_refresh_token(email, user_id, expires_delta)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Decode and verify token content
        import jwt
        decoded = jwt.decode(token, "test_secret_key", algorithms=["HS256"])
        assert decoded["sub"] == email
        assert decoded["id"] == str(user_id)
        assert decoded["type"] == "refresh"


class TestAuthenticateUser:
    """Test user authentication"""

    @patch("routes.v1.auth.service.users_db_service.get_user_by_email")
    def test_authenticate_user_success(self, mock_get_user):
        """Test successful user authentication"""
        # Mock user with hashed password
        mock_user = Mock()
        mock_user.password = get_password_hash("testPassword@123")
        mock_get_user.return_value = mock_user
        
        mock_db = Mock()
        result = authenticate_user("<EMAIL>", "testPassword@123", mock_db)
        
        assert result == mock_user
        mock_get_user.assert_called_once_with(mock_db, "<EMAIL>")

    @patch("routes.v1.auth.service.users_db_service.get_user_by_email")
    def test_authenticate_user_not_found(self, mock_get_user):
        """Test authentication with non-existent user"""
        mock_get_user.return_value = None
        
        mock_db = Mock()
        result = authenticate_user("<EMAIL>", "password", mock_db)
        
        assert result is False
        mock_get_user.assert_called_once_with(mock_db, "<EMAIL>")

    @patch("routes.v1.auth.service.users_db_service.get_user_by_email")
    def test_authenticate_user_wrong_password(self, mock_get_user):
        """Test authentication with wrong password"""
        # Mock user with different password hash
        mock_user = Mock()
        mock_user.password = get_password_hash("correctPassword@123")
        mock_get_user.return_value = mock_user
        
        mock_db = Mock()
        result = authenticate_user("<EMAIL>", "wrongPassword@123", mock_db)
        
        assert result is False
        mock_get_user.assert_called_once_with(mock_db, "<EMAIL>")


class TestCreateUser:
    """Test user creation service"""

    @patch("routes.v1.auth.service.users_db_service.create_user")
    def test_create_user_success(self, mock_create_user):
        """Test successful user creation"""
        # Mock the database response
        mock_created_user = Mock()
        mock_created_user.id = uuid4()
        mock_created_user.email = "<EMAIL>"
        mock_created_user.first_name = "Test"
        mock_created_user.last_name = "User"
        mock_created_user.is_active = True
        mock_create_user.return_value = mock_created_user
        
        # Create request
        request = CreateUserRequest(
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            password="testPassword@123"
        )
        
        mock_db = Mock()
        result = create_user(request, mock_db)
        
        # Verify result
        assert result.email == "<EMAIL>"
        assert result.first_name == "Test"
        assert result.last_name == "User"
        assert result.is_active is True
        
        # Verify database call
        mock_create_user.assert_called_once()
        created_user_arg = mock_create_user.call_args[0][1]  # Second argument (first is db)
        assert created_user_arg.email == "<EMAIL>"
        assert created_user_arg.first_name == "Test"
        assert created_user_arg.last_name == "User"
        assert verify_password("testPassword@123", created_user_arg.password)

    @patch("app.routes.v1.auth.service.users_db_service.create_user")
    def test_create_user_duplicate_email(self, mock_create_user):
        """Test user creation with duplicate email"""
        # Mock IntegrityError for duplicate email
        mock_create_user.side_effect = IntegrityError("", "", "")
        
        request = CreateUserRequest(
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            password="testPassword@123"
        )
        
        mock_db = Mock()
        
        with pytest.raises(HTTPException) as exc_info:
            create_user(request, mock_db)
        
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert "User with this email already exists" in str(exc_info.value.detail)
        mock_db.rollback.assert_called_once()

    @patch("app.routes.v1.auth.service.users_db_service.create_user")
    def test_create_user_database_error(self, mock_create_user):
        """Test user creation with database error"""
        # Mock generic database error
        mock_create_user.side_effect = Exception("Database connection failed")
        
        request = CreateUserRequest(
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            password="testPassword@123"
        )
        
        mock_db = Mock()
        
        with pytest.raises(Exception) as exc_info:
            create_user(request, mock_db)
        
        assert "Database connection failed" in str(exc_info.value)
        mock_db.rollback.assert_called_once()
