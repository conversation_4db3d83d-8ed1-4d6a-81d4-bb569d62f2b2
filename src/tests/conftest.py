import os
import sys
from unittest.mock import Mock, patch
from uuid import uuid4

import pytest
from dotenv import load_dotenv

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
env_path = os.path.join(ROOT_DIR, ".env")
load_dotenv(env_path)

# Override environment variables for tests  
os.environ["SECRET_KEY"] = "test_secret_key"
os.environ["ALGORITHM"] = "HS256"
os.environ["DATABASE_URL"] = "sqlite:///:memory:"
os.environ["SKIP_DB_INIT"] = "true"  # Flag to skip database initialization

sys.path.append(os.path.join(ROOT_DIR, "app"))


@pytest.fixture(scope="function")
def mock_current_user():
    """Mock current user for authentication"""
    user_mock = Mock()
    user_mock.user_id = uuid4()  # Use user_id instead of id for consistency with service layer
    user_mock.id = str(uuid4())
    user_mock.email = "<EMAIL>"
    user_mock.first_name = "Test"
    user_mock.last_name = "User"
    user_mock.is_active = True
    return user_mock


@pytest.fixture(scope="function")
def client(mock_current_user):
    """Test client with mocked dependencies"""

    from fastapi.testclient import TestClient

    from app.main import app
    from app.routes.v1.auth.service import get_current_user

    # Create a more comprehensive mock session that prevents database access
    def mock_get_db():
        mock_session = Mock()
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.rollback = Mock()
        mock_session.refresh = Mock()
        mock_session.close = Mock()

        # Mock query to prevent actual database queries
        mock_query = Mock()
        mock_query.filter = Mock(return_value=mock_query)
        mock_query.filter_by = Mock(return_value=mock_query)
        mock_query.all = Mock(return_value=[])
        mock_query.first = Mock(return_value=None)
        mock_query.count = Mock(return_value=0)
        mock_query.offset = Mock(return_value=mock_query)
        mock_query.limit = Mock(return_value=mock_query)
        mock_session.query = Mock(return_value=mock_query)

        return mock_session

    # Override the dependencies at the app level
    from app.database.core import get_db
    app.dependency_overrides[get_db] = mock_get_db
    app.dependency_overrides[get_current_user] = lambda: mock_current_user

    # Reset rate limiter if it exists
    try:
        from app.utils.rate_limiter import limiter
        limiter.reset()
    except (ImportError, AttributeError):
        pass

    try:
        with TestClient(app) as test_client:
            yield test_client
    finally:
        # Clean up dependency overrides
        app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def auth_headers():
    """Create valid auth headers with a proper JWT token for testing"""
    from datetime import datetime, timedelta, timezone

    import jwt
    
    # Use the same secret as in the app
    secret_key = "test_secret_key"
    
    # Create a valid JWT token with the expected payload structure
    payload = {
        "id": str(uuid4()),  # The service expects "id" not "user_id"
        "type": "access",    # The service expects "type" field
        "exp": datetime.now(timezone.utc) + timedelta(hours=1)
    }
    
    token = jwt.encode(payload, secret_key, algorithm="HS256")
    return {"Authorization": f"Bearer {token}"}