"""create_customer_id_generation_function

Revision ID: 50ae4564049e
Revises: 
Create Date: 2025-09-19 09:46:43.133451

"""
from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '50ae4564049e'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Step 1: Create a dedicated sequence for customer IDs
    op.execute("""
        CREATE SEQUENCE IF NOT EXISTS customer_id_sequence
            START WITH 1
            INCREMENT BY 1
            NO MAXVALUE
            CACHE 1;
    """)
    
    # Step 2: Create a function to generate the formatted customer ID
    op.execute("""
        CREATE OR REPLACE FUNCTION generate_customer_id()
        RETURNS TEXT AS $$
        BEGIN
            RETURN 'EUWI-' || LPAD(nextval('customer_id_sequence')::TEXT, 4, '0');
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # Step 3: Create a trigger function that sets customer_id on INSERT
    op.execute("""
        CREATE OR REPLACE FUNCTION set_customer_id()
        RETURNS TRIGGER AS $$
        BEGIN
            -- Only set customer_id if it's NULL (allows manual override if needed)
            IF NEW.customer_id IS NULL THEN
                NEW.customer_id := generate_customer_id();
            END IF;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # Step 4: Create the trigger on the customers table (if it exists)
    op.execute("""
        DROP TRIGGER IF EXISTS trigger_set_customer_id ON customers;
        CREATE TRIGGER trigger_set_customer_id
            BEFORE INSERT ON customers
            FOR EACH ROW
            EXECUTE FUNCTION set_customer_id();
    """)
    
    # Step 5: Set the sequence to start from the next available number
    # This ensures no conflicts with existing customer IDs
    op.execute("""
        DO $$
        DECLARE
            max_num INTEGER;
        BEGIN
            -- Check if customers table exists first
            IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'customers') THEN
                -- Find the highest existing customer number
                SELECT COALESCE(MAX(
                    CASE 
                        WHEN customer_id ~ '^EUWI-\\d+$' 
                        THEN CAST(SUBSTRING(customer_id FROM 6) AS INTEGER)
                        ELSE 0
                    END
                ), 0) INTO max_num
                FROM customers 
                WHERE customer_id IS NOT NULL;
                
                -- Set sequence to start from the next number
                PERFORM setval('customer_id_sequence', max_num + 1, false);
            END IF;
        END $$;
    """)


def downgrade() -> None:
    """Downgrade schema."""
    # Drop the trigger
    op.execute("""
        DROP TRIGGER IF EXISTS trigger_set_customer_id ON customers;
    """)
    
    # Drop the trigger function
    op.execute("""
        DROP FUNCTION IF EXISTS set_customer_id();
    """)
    
    # Drop the customer ID generation function
    op.execute("""
        DROP FUNCTION IF EXISTS generate_customer_id();
    """)
    
    # Drop the sequence
    op.execute("""
        DROP SEQUENCE IF EXISTS customer_id_sequence;
    """)
