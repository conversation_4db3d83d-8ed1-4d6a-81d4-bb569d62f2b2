"""add is_active field to users table

Revision ID: add_is_active_to_users
Revises: 50ae4564049e
Create Date: 2025-09-19 15:30:00.000000

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = 'add_is_active_to_users'
down_revision: Union[str, Sequence[str], None] = '50ae4564049e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add is_active column to users table."""
    # Add is_active column with default value True
    op.add_column('users', sa.Column('is_active', sa.<PERSON>(), nullable=False, server_default='true'))


def downgrade() -> None:
    """Remove is_active column from users table."""
    op.drop_column('users', 'is_active')
